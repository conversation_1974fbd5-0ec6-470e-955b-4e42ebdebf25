'use client';

import { motion, LazyMotion, domAnimation, m } from 'framer-motion';
import { ReactNode } from 'react';

// Optimized motion provider - only loads necessary features
export function OptimizedMotionProvider({ children }: { children: ReactNode }) {
  return (
    <LazyMotion features={domAnimation} strict>
      {children}
    </LazyMotion>
  );
}

// Optimized motion components using 'm' instead of 'motion'
export const OptimizedMotion = {
  div: m.div,
  section: m.section,
  button: m.button,
  span: m.span,
  h1: m.h1,
  h2: m.h2,
  h3: m.h3,
  p: m.p,
  img: m.img,
  svg: m.svg,
};

// Performance-optimized animation presets
export const optimizedAnimations = {
  // Use transform instead of layout animations for better performance
  fadeInUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  
  fadeInScale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  
  slideInLeft: {
    initial: { opacity: 0, x: -30 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  
  slideInRight: {
    initial: { opacity: 0, x: 30 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  
  // Hover animations with will-change optimization
  hoverScale: {
    whileHover: { 
      scale: 1.05,
      transition: { duration: 0.2, ease: "easeOut" }
    },
    whileTap: { 
      scale: 0.95,
      transition: { duration: 0.1, ease: "easeOut" }
    },
    style: { willChange: 'transform' }
  },
  
  // Stagger with optimized timing
  staggerContainer: {
    animate: {
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1
      }
    }
  },
  
  staggerItem: {
    initial: { opacity: 0, y: 20 },
    animate: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
    }
  }
};

// High-performance floating animation
export function OptimizedFloatingElement({ 
  children, 
  className = '',
  intensity = 10,
  duration = 3,
  delay = 0 
}: {
  children: ReactNode;
  className?: string;
  intensity?: number;
  duration?: number;
  delay?: number;
}) {
  return (
    <m.div
      className={className}
      animate={{
        y: [0, -intensity, 0],
      }}
      transition={{
        duration,
        repeat: Infinity,
        delay,
        ease: "easeInOut",
        repeatType: "reverse"
      }}
      style={{ willChange: 'transform' }}
    >
      {children}
    </m.div>
  );
}

// Optimized scroll-triggered animation
export function OptimizedScrollAnimation({ 
  children, 
  className = '',
  animation = 'fadeInUp',
  delay = 0 
}: {
  children: ReactNode;
  className?: string;
  animation?: keyof typeof optimizedAnimations;
  delay?: number;
}) {
  const animationProps = optimizedAnimations[animation];
  
  return (
    <m.div
      className={className}
      initial={animationProps.initial}
      whileInView={animationProps.animate}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ 
        ...animationProps.transition, 
        delay 
      }}
      style={{ willChange: 'transform, opacity' }}
    >
      {children}
    </m.div>
  );
}

// Optimized button with magnetic effect
export function OptimizedMagneticButton({ 
  children, 
  className = '',
  onClick,
  ...props 
}: {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  [key: string]: any;
}) {
  return (
    <m.button
      className={className}
      onClick={onClick}
      whileHover={{ 
        scale: 1.02,
        y: -2,
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      whileTap={{ 
        scale: 0.98,
        y: 0,
        transition: { duration: 0.1, ease: "easeOut" }
      }}
      style={{ willChange: 'transform' }}
      {...props}
    >
      {children}
    </m.button>
  );
}

// Optimized card with hover effects
export function OptimizedCard({ 
  children, 
  className = '',
  ...props 
}: {
  children: ReactNode;
  className?: string;
  [key: string]: any;
}) {
  return (
    <m.div
      className={className}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      whileHover={{ 
        y: -5,
        transition: { duration: 0.3, ease: "easeOut" }
      }}
      transition={{ duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }}
      style={{ willChange: 'transform, opacity' }}
      {...props}
    >
      {children}
    </m.div>
  );
}

// Performance tips component
export function PerformanceTips() {
  return (
    <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-6">
      <h4 className="text-yellow-400 font-semibold mb-2">🚀 Performance Tips:</h4>
      <ul className="text-yellow-300/80 text-sm space-y-1">
        <li>• Sử dụng LazyMotion để giảm bundle size</li>
        <li>• Dùng transform thay vì layout animations</li>
        <li>• Thêm will-change CSS cho các element animation</li>
        <li>• Sử dụng viewport={{ once: true }} cho scroll animations</li>
        <li>• Tối ưu ease curves cho animation mượt mà</li>
      </ul>
    </div>
  );
}
