'use client';

import { motion } from 'framer-motion';
import { useEffect, useState } from 'react';

// Floating particles background
export function FloatingParticles({ 
  count = 50, 
  className = '' 
}: { 
  count?: number; 
  className?: string; 
}) {
  const [particles, setParticles] = useState<Array<{
    id: number;
    x: number;
    y: number;
    size: number;
    color: string;
    duration: number;
    delay: number;
  }>>([]);

  useEffect(() => {
    const colors = [
      'rgba(249, 115, 22, 0.6)', // orange
      'rgba(59, 130, 246, 0.6)', // blue
      'rgba(168, 85, 247, 0.6)', // purple
      'rgba(34, 197, 94, 0.6)',  // green
      'rgba(236, 72, 153, 0.6)', // pink
    ];

    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      size: Math.random() * 4 + 1,
      color: colors[Math.floor(Math.random() * colors.length)],
      duration: Math.random() * 3 + 2,
      delay: Math.random() * 2,
    }));

    setParticles(newParticles);
  }, [count]);

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full blur-sm"
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
          }}
          animate={{
            y: [0, -30, 0],
            opacity: [0.3, 1, 0.3],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: particle.duration,
            repeat: Infinity,
            delay: particle.delay,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}

// Animated gradient background
export function AnimatedGradient({ 
  className = '' 
}: { 
  className?: string; 
}) {
  return (
    <motion.div
      className={`absolute inset-0 ${className}`}
      style={{
        background: `
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)
        `,
      }}
      animate={{
        background: [
          `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
           radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
           radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)`,
          `radial-gradient(circle at 80% 20%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
           radial-gradient(circle at 20% 80%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
           radial-gradient(circle at 60% 60%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)`,
          `radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
           radial-gradient(circle at 60% 60%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
           radial-gradient(circle at 20% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%)`,
        ],
      }}
      transition={{
        duration: 8,
        repeat: Infinity,
        ease: "linear",
      }}
    />
  );
}

// Geometric shapes animation
export function GeometricShapes({ 
  className = '' 
}: { 
  className?: string; 
}) {
  const shapes = [
    { type: 'circle', size: 100, color: 'rgba(59, 130, 246, 0.1)' },
    { type: 'square', size: 80, color: 'rgba(249, 115, 22, 0.1)' },
    { type: 'triangle', size: 60, color: 'rgba(168, 85, 247, 0.1)' },
    { type: 'circle', size: 120, color: 'rgba(34, 197, 94, 0.1)' },
  ];

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {shapes.map((shape, index) => (
        <motion.div
          key={index}
          className="absolute"
          style={{
            left: `${20 + index * 20}%`,
            top: `${10 + index * 15}%`,
            width: shape.size,
            height: shape.size,
            backgroundColor: shape.color,
            borderRadius: shape.type === 'circle' ? '50%' : shape.type === 'triangle' ? '0' : '10px',
            clipPath: shape.type === 'triangle' ? 'polygon(50% 0%, 0% 100%, 100% 100%)' : 'none',
          }}
          animate={{
            rotate: [0, 360],
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.7, 0.3],
          }}
          transition={{
            duration: 10 + index * 2,
            repeat: Infinity,
            ease: "linear",
          }}
        />
      ))}
    </div>
  );
}

// Wave animation
export function WaveAnimation({ 
  className = '' 
}: { 
  className?: string; 
}) {
  return (
    <div className={`absolute bottom-0 left-0 right-0 ${className}`}>
      <svg
        viewBox="0 0 1200 120"
        preserveAspectRatio="none"
        className="relative block w-full h-16"
      >
        <motion.path
          d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
          fill="rgba(59, 130, 246, 0.1)"
          animate={{
            d: [
              "M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z",
              "M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z",
              "M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
            ]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </svg>
    </div>
  );
}

// Matrix rain effect
export function MatrixRain({ 
  className = '' 
}: { 
  className?: string; 
}) {
  const [drops, setDrops] = useState<Array<{
    id: number;
    x: number;
    chars: string[];
    speed: number;
  }>>([]);

  useEffect(() => {
    const characters = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
    const newDrops = Array.from({ length: 20 }, (_, i) => ({
      id: i,
      x: (i * 5) % 100,
      chars: Array.from({ length: 10 }, () => 
        characters[Math.floor(Math.random() * characters.length)]
      ),
      speed: Math.random() * 2 + 1,
    }));

    setDrops(newDrops);
  }, []);

  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {drops.map((drop) => (
        <motion.div
          key={drop.id}
          className="absolute text-green-400 font-mono text-xs opacity-30"
          style={{ left: `${drop.x}%` }}
          animate={{
            y: ['-100%', '100vh'],
          }}
          transition={{
            duration: 10 / drop.speed,
            repeat: Infinity,
            ease: "linear",
          }}
        >
          {drop.chars.map((char, index) => (
            <div key={index} className="block">
              {char}
            </div>
          ))}
        </motion.div>
      ))}
    </div>
  );
}
