'use client';

import { motion, HTMLMotionProps } from 'framer-motion';
import { ReactNode } from 'react';
import { 
  fadeInUp, 
  fadeInLeft, 
  fadeInRight, 
  scaleIn, 
  staggerContainer, 
  staggerItem 
} from '@/hooks/use-scroll-animation';

interface AnimatedSectionProps extends Omit<HTMLMotionProps<'div'>, 'children'> {
  children: ReactNode;
  animation?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn' | 'stagger';
  delay?: number;
  duration?: number;
  className?: string;
}

const animationVariants = {
  fadeInUp,
  fadeInLeft,
  fadeInRight,
  scaleIn,
  stagger: staggerContainer
};

export function AnimatedSection({ 
  children, 
  animation = 'fadeInUp', 
  delay = 0,
  duration = 0.6,
  className = '',
  ...props 
}: AnimatedSectionProps) {
  const variants = animationVariants[animation];
  
  // Custom variants with delay and duration
  const customVariants = {
    hidden: {
      ...variants.hidden,
      transition: { duration, ease: "easeOut", delay }
    },
    visible: {
      ...variants.visible,
      transition: { 
        duration, 
        ease: "easeOut", 
        delay: animation === 'stagger' ? 0 : delay,
        ...(animation === 'stagger' && {
          staggerChildren: 0.1,
          delayChildren: delay
        })
      }
    }
  };

  return (
    <motion.div
      className={className}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: "-100px" }}
      variants={customVariants}
      {...props}
    >
      {children}
    </motion.div>
  );
}

// Stagger item component for use within stagger containers
export function AnimatedItem({ 
  children, 
  className = '',
  ...props 
}: Omit<AnimatedSectionProps, 'animation'>) {
  return (
    <motion.div
      className={className}
      variants={staggerItem}
      {...props}
    >
      {children}
    </motion.div>
  );
}

// Floating animation component
export function FloatingElement({ 
  children, 
  className = '',
  intensity = 20,
  duration = 3,
  delay = 0,
  ...props 
}: AnimatedSectionProps & { intensity?: number }) {
  return (
    <motion.div
      className={className}
      animate={{
        y: [0, -intensity, 0],
        opacity: [0.3, 1, 0.3],
      }}
      transition={{ 
        duration, 
        repeat: Infinity, 
        delay,
        ease: "easeInOut"
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
}

// Hover scale component
export function HoverScale({ 
  children, 
  className = '',
  scale = 1.05,
  ...props 
}: AnimatedSectionProps & { scale?: number }) {
  return (
    <motion.div
      className={className}
      whileHover={{ scale }}
      whileTap={{ scale: scale * 0.95 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
      {...props}
    >
      {children}
    </motion.div>
  );
}

// Reveal text animation
export function RevealText({ 
  children, 
  className = '',
  delay = 0,
  ...props 
}: AnimatedSectionProps) {
  return (
    <motion.div
      className={`overflow-hidden ${className}`}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      {...props}
    >
      <motion.div
        variants={{
          hidden: { y: "100%" },
          visible: { 
            y: 0,
            transition: { 
              duration: 0.8, 
              ease: "easeOut",
              delay 
            }
          }
        }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
}

// Magnetic button effect
export function MagneticButton({ 
  children, 
  className = '',
  ...props 
}: AnimatedSectionProps) {
  return (
    <motion.button
      className={className}
      whileHover={{ 
        scale: 1.05,
        boxShadow: "0 10px 30px rgba(0,0,0,0.2)"
      }}
      whileTap={{ scale: 0.95 }}
      transition={{ 
        type: "spring", 
        stiffness: 400, 
        damping: 17 
      }}
      {...props}
    >
      {children}
    </motion.button>
  );
}

// Parallax scroll effect
export function ParallaxElement({ 
  children, 
  className = '',
  speed = 0.5,
  ...props 
}: AnimatedSectionProps & { speed?: number }) {
  return (
    <motion.div
      className={className}
      style={{
        y: `${speed * -100}%`
      }}
      {...props}
    >
      {children}
    </motion.div>
  );
}
