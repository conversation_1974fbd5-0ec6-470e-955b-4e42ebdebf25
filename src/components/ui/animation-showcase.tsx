'use client';

import { 
  AnimatedSection, 
  AnimatedItem, 
  FloatingElement, 
  HoverScale, 
  RevealText, 
  MagneticButton,
  FloatingParticles,
  AnimatedGradient,
  GeometricShapes,
  WaveAnimation
} from '@/components/ui';

export function AnimationShowcase() {
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 overflow-hidden">
      {/* Background Animations */}
      <AnimatedGradient />
      <FloatingParticles count={30} />
      <GeometricShapes />
      
      <div className="relative z-10 container mx-auto px-4 py-20">
        {/* Hero Section */}
        <AnimatedSection animation="fadeInUp" className="text-center mb-20">
          <RevealText className="text-6xl font-bold text-white mb-6">
            Framer Motion
          </RevealText>
          <RevealText delay={0.2} className="text-2xl text-orange-400 mb-8">
            Animation Showcase
          </RevealText>
          <AnimatedSection animation="scaleIn" delay={0.4}>
            <p className="text-xl text-white/70 max-w-2xl mx-auto">
              <PERSON>hám phá sức mạnh của framer-motion với các hiệu ứng animation đẹp mắt và mượt mà
            </p>
          </AnimatedSection>
        </AnimatedSection>

        {/* Animation Examples Grid */}
        <AnimatedSection animation="stagger" className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          <AnimatedItem>
            <HoverScale className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6 h-64">
              <h3 className="text-xl font-bold text-white mb-4">Hover Scale</h3>
              <p className="text-white/70">Hover để xem hiệu ứng scale mượt mà</p>
              <div className="mt-4">
                <div className="w-12 h-12 bg-blue-500 rounded-full mx-auto"></div>
              </div>
            </HoverScale>
          </AnimatedItem>

          <AnimatedItem>
            <div className="bg-gradient-to-br from-green-500/20 to-teal-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6 h-64 relative overflow-hidden">
              <h3 className="text-xl font-bold text-white mb-4">Floating Elements</h3>
              <p className="text-white/70">Các phần tử bay lơ lửng tự nhiên</p>
              <FloatingElement intensity={15} duration={2} className="absolute top-16 right-6">
                <div className="w-8 h-8 bg-green-400 rounded-full"></div>
              </FloatingElement>
              <FloatingElement intensity={20} duration={3} delay={1} className="absolute bottom-16 left-6">
                <div className="w-6 h-6 bg-teal-400 rounded-full"></div>
              </FloatingElement>
            </div>
          </AnimatedItem>

          <AnimatedItem>
            <div className="bg-gradient-to-br from-pink-500/20 to-rose-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6 h-64">
              <h3 className="text-xl font-bold text-white mb-4">Reveal Text</h3>
              <RevealText className="text-white/70 mb-4">
                Text xuất hiện từ dưới lên
              </RevealText>
              <RevealText delay={0.3} className="text-pink-400">
                Với delay khác nhau
              </RevealText>
            </div>
          </AnimatedItem>

          <AnimatedItem>
            <div className="bg-gradient-to-br from-orange-500/20 to-yellow-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6 h-64">
              <h3 className="text-xl font-bold text-white mb-4">Fade In Directions</h3>
              <AnimatedSection animation="fadeInLeft" className="text-white/70 mb-2">
                ← Fade In Left
              </AnimatedSection>
              <AnimatedSection animation="fadeInRight" className="text-white/70 mb-2">
                Fade In Right →
              </AnimatedSection>
              <AnimatedSection animation="fadeInUp" className="text-orange-400">
                ↑ Fade In Up
              </AnimatedSection>
            </div>
          </AnimatedItem>

          <AnimatedItem>
            <div className="bg-gradient-to-br from-purple-500/20 to-indigo-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6 h-64 flex flex-col justify-center items-center">
              <h3 className="text-xl font-bold text-white mb-6">Magnetic Button</h3>
              <MagneticButton className="px-6 py-3 bg-gradient-to-r from-purple-500 to-indigo-500 text-white rounded-xl font-medium">
                Click Me!
              </MagneticButton>
            </div>
          </AnimatedItem>

          <AnimatedItem>
            <div className="bg-gradient-to-br from-cyan-500/20 to-blue-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-6 h-64">
              <h3 className="text-xl font-bold text-white mb-4">Scale In</h3>
              <AnimatedSection animation="scaleIn" delay={0.5} className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full mx-auto mb-4"></div>
                <p className="text-white/70">Hiệu ứng scale từ nhỏ đến lớn</p>
              </AnimatedSection>
            </div>
          </AnimatedItem>
        </AnimatedSection>

        {/* Interactive Demo Section */}
        <AnimatedSection animation="fadeInUp" className="text-center mb-20">
          <h2 className="text-4xl font-bold text-white mb-8">Interactive Demo</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <HoverScale className="bg-gradient-to-br from-red-500/20 to-pink-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-4">Service Card</h3>
              <p className="text-white/70 mb-6">
                Thiết kế website chuyên nghiệp với hiệu ứng hover mượt mà
              </p>
              <MagneticButton className="px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-xl font-medium">
                Tìm hiểu thêm
              </MagneticButton>
            </HoverScale>

            <HoverScale className="bg-gradient-to-br from-emerald-500/20 to-green-500/20 backdrop-blur-xl border border-white/10 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-4">Premium Account</h3>
              <p className="text-white/70 mb-6">
                Tài khoản premium chất lượng cao với bảo hành trọn đời
              </p>
              <MagneticButton className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-500 text-white rounded-xl font-medium">
                Đặt hàng ngay
              </MagneticButton>
            </HoverScale>
          </div>
        </AnimatedSection>

        {/* Stagger Animation Demo */}
        <AnimatedSection animation="stagger" className="mb-20">
          <h2 className="text-4xl font-bold text-white text-center mb-12">Stagger Animation</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Array.from({ length: 8 }, (_, i) => (
              <AnimatedItem key={i}>
                <div className="bg-gradient-to-br from-violet-500/20 to-purple-500/20 backdrop-blur-xl border border-white/10 rounded-xl p-6 text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-violet-400 to-purple-400 rounded-full mx-auto mb-4"></div>
                  <p className="text-white font-medium">Item {i + 1}</p>
                </div>
              </AnimatedItem>
            ))}
          </div>
        </AnimatedSection>
      </div>

      {/* Wave Animation at bottom */}
      <WaveAnimation />
    </div>
  );
}
