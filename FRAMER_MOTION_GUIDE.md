# Framer Motion Guide - Global Resource

## 📚 Tổng quan

Dự án Global Resource đã được tích hợp đầy đủ với **Framer Motion** - thư viện animation mạnh mẽ cho React. Hướng dẫn này sẽ giúp bạn hiểu và sử dụng hiệu quả các animation trong dự án.

## 🚀 Cài đặt và Cấu hình

### Thư viện đã được cài đặt:
```json
{
  "framer-motion": "^12.23.3",
  "@react-spring/web": "^10.0.1",
  "@use-gesture/react": "^10.3.1"
}
```

### Cấu hình tối ưu:
- ✅ `OptimizedMotionProvider` đã được setup trong `layout.tsx`
- ✅ `LazyMotion` để giảm bundle size
- ✅ Performance optimization với `will-change` CSS

## 🎨 Components Animation Có Sẵn

### 1. Basic Animation Components

```tsx
import { AnimatedSection, AnimatedItem, FloatingElement } from '@/components/ui';

// Fade in animation
<AnimatedSection animation="fadeInUp" delay={0.2}>
  <h1>Tiêu đề với animation</h1>
</AnimatedSection>

// Stagger animation cho danh sách
<AnimatedSection animation="stagger">
  <AnimatedItem>Item 1</AnimatedItem>
  <AnimatedItem>Item 2</AnimatedItem>
  <AnimatedItem>Item 3</AnimatedItem>
</AnimatedSection>

// Floating element
<FloatingElement intensity={20} duration={3}>
  <div>Phần tử bay lơ lửng</div>
</FloatingElement>
```

### 2. Interactive Components

```tsx
import { HoverScale, MagneticButton, RevealText } from '@/components/ui';

// Hover scale effect
<HoverScale scale={1.05}>
  <div className="card">Hover để scale</div>
</HoverScale>

// Magnetic button
<MagneticButton className="btn-primary">
  Click me!
</MagneticButton>

// Reveal text animation
<RevealText delay={0.3}>
  Text xuất hiện từ dưới lên
</RevealText>
```

### 3. Background Animations

```tsx
import { 
  FloatingParticles, 
  AnimatedGradient, 
  GeometricShapes,
  WaveAnimation 
} from '@/components/ui';

// Background với particles
<div className="relative">
  <FloatingParticles count={50} />
  <AnimatedGradient />
  <GeometricShapes />
  <WaveAnimation />
  {/* Content */}
</div>
```

### 4. Optimized Components (Hiệu suất cao)

```tsx
import { 
  OptimizedScrollAnimation,
  OptimizedMagneticButton,
  OptimizedCard 
} from '@/components/ui';

// Scroll animation tối ưu
<OptimizedScrollAnimation animation="fadeInUp">
  <div>Content</div>
</OptimizedScrollAnimation>

// Button tối ưu
<OptimizedMagneticButton>
  Optimized Button
</OptimizedMagneticButton>

// Card với hover effect
<OptimizedCard>
  <h3>Card Title</h3>
  <p>Card content</p>
</OptimizedCard>
```

## 🎯 Animation Patterns Có Sẵn

### Fade Animations
- `fadeInUp` - Fade in từ dưới lên
- `fadeInLeft` - Fade in từ trái
- `fadeInRight` - Fade in từ phải
- `scaleIn` - Scale từ nhỏ đến lớn

### Stagger Animations
- `staggerContainer` - Container cho stagger effect
- `staggerItem` - Item trong stagger container

### Hover Effects
- `hoverScale` - Scale khi hover
- Magnetic button effect
- Card hover animations

## 📱 Responsive Animation

```tsx
// Animation khác nhau cho mobile/desktop
<motion.div
  initial={{ opacity: 0, y: 50 }}
  animate={{ opacity: 1, y: 0 }}
  transition={{ 
    duration: 0.6,
    ease: [0.25, 0.46, 0.45, 0.94] // Custom easing
  }}
  // Responsive animation
  className="lg:hover:scale-105 transition-transform"
>
  Content
</motion.div>
```

## ⚡ Performance Tips

### 1. Sử dụng LazyMotion
```tsx
import { LazyMotion, domAnimation, m } from 'framer-motion';

<LazyMotion features={domAnimation}>
  <m.div>Optimized motion component</m.div>
</LazyMotion>
```

### 2. Will-change CSS
```tsx
<motion.div
  style={{ willChange: 'transform' }}
  animate={{ x: 100 }}
>
  Optimized transform
</motion.div>
```

### 3. Viewport Optimization
```tsx
<motion.div
  initial={{ opacity: 0 }}
  whileInView={{ opacity: 1 }}
  viewport={{ once: true, margin: "-100px" }}
>
  Scroll animation chỉ chạy 1 lần
</motion.div>
```

## 🎪 Demo Page

Truy cập `/animation-demo` để xem showcase đầy đủ các animation:
- Basic animations
- Interactive components
- Background effects
- Performance optimizations

## 🔧 Custom Animation

### Tạo animation variants mới:
```tsx
const customVariants = {
  hidden: { 
    opacity: 0, 
    scale: 0.8,
    rotate: -10
  },
  visible: { 
    opacity: 1, 
    scale: 1,
    rotate: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

<motion.div
  variants={customVariants}
  initial="hidden"
  animate="visible"
>
  Custom animation
</motion.div>
```

### Gesture animations:
```tsx
import { useDrag } from '@use-gesture/react';

const bind = useDrag(({ offset: [x, y] }) => {
  // Handle drag
});

<motion.div {...bind()}>
  Draggable element
</motion.div>
```

## 🎨 Animation Best Practices

1. **Sử dụng easing curves phù hợp**
   - `ease: [0.25, 0.46, 0.45, 0.94]` cho smooth animations
   - `ease: "easeOut"` cho hover effects

2. **Timing tối ưu**
   - 0.2-0.3s cho hover effects
   - 0.5-0.8s cho page transitions
   - 2-4s cho floating animations

3. **Stagger timing**
   - 0.08-0.1s delay giữa các items
   - 0.1-0.2s delayChildren

4. **Mobile optimization**
   - Giảm intensity animation trên mobile
   - Sử dụng `prefers-reduced-motion`

## 🚀 Kết luận

Framer Motion đã được tích hợp hoàn chỉnh vào dự án Global Resource với:
- ✅ Performance optimization
- ✅ Responsive design
- ✅ Reusable components
- ✅ Best practices implementation

Sử dụng các component có sẵn để tạo ra những animation đẹp mắt và mượt mà cho website của bạn!
